需求洞察
需求洞察是概念工程的核心使能流程
需求洞察是概念工程的核心使能流程
需求洞察的四大目标、三大步骤、五个核心工具
从需求分析到需求洞察
内容
1、需求分析与需求洞察的区别
2、需求洞察的四大目标
3、需求洞察的步骤和工具
内容
1、需求分析与需求洞察的区别
2、需求洞察的四大目标
3、需求洞察的步骤和工具
需求洞察不是需求分析
需求洞察的目的是为了正确的理解客户需求，为产品创新找到正确的需求组合和解法路径
洞察思维
洞察方法
洞察
力
洞察什么
洞察需基于三个维度的结合：
洞察思维（能力）+洞察方法（工具）+洞察什么（目标）
需求分析是需求管理的子流程
需求分析关注需求的…
需求是对产品或过程的操作、功能和设计的特性或约束的表述，这些表
述是明确的、可测试的、可度量的，而且对于产品或过程的可接受性（被顾客
或内部质量保证措施）来说是必须的。
——IEEE1220-1998
需求分析的本质是需求转化（还原、翻译、打标签和合并…）
示例：需求卡
需求卡
需求名称
需求
基本信息
需求编号XXX
需求分类
全新产品/方案(人有我无)
全新产品/方案(人无我无)
新功能特性(人有我无) 
新功能特性(人无我无) 
现有产品改善
质量提升，成本优化，生产制造，
可服务性，效率提升，安全、合规，
可采购性，Bug
需求提交者系统代填
涉及产品线提供选项
需求标签（针对需求内容的关键词）TCI利旧，软件兼容性….
是否为生死需求是？否？
客户
基本信息
客户单位名称
客户姓名&职务
细分行业提供选项
用户问题
/想法
要做什么事，达成什么目标？
为了达成这个目标，谁一步步是如何做的？有业务流程图最佳
在这个过程中遇到了什么问题（或有什么想法），现
在是怎么处理的？
为什么有这个问题和想法？
这个问题目前还有什么影响或损失？作为强度评分信息
这个问题多久发生一次？
作为频度评分信息
选项：每天/每周1次货多次；每月1次或
多次；每季度/年1次或多次
希望解决到什么程度？（最基本/最理想的）
基本效果：可满足客户完成任务的基本要
求
理想效果：明显优于对手，并极大提升用
户完成任务的效率、体验等价值
备注：可衡量、完整、有挑战、限制条件
愿意付出多大代价来解决这个问题或实现这个想法？
需求卡
其他客户
情况
同行业还有哪些用户存在类似问题（想
法）？作为广度评分信息
其他行业还有哪些客户存在类似问题（想
法）？作为广度评分信息
四度评分
战略匹配度评分（产品经理）-125、0、125
强度评分（造成的影响）1-5分
广度评分（普遍性）1-5分
频度评分（发生频率）1-5分
需求价值评分战略匹配度+强度*频度
*广度
可能的需
求解法
您或客户建议的解法是什么？
竞争情况说明
选项：友商有解法，满
足用户需求；友商有解
法，解的不好；友商没
有对应解法
友商及其解法是什么？友商1，解法：XXX友
商2，解法：XXX
需求洞察是概念工程的核心使能
需求洞察归属需求管理体系，是概念工程的使能流程，支撑项目支柱、价值主张和初始概念生成。
需求洞察关注需求的…
客户的思考模式始于“发现自己需要完成一个任务”，然后就开始“雇佣”能
够帮助他们有效地、便捷地、便宜地完成这一任务的东西。
明确定义的“待办任务”可提供一份创新蓝图，这份蓝图与传统营销中的“需
求”概念截然不同，因为待办任务对于你想解决的问题的定位更加精准。 
“待办任务”理论是一个整合工具，是理解影响消费者在特定情境下决策的复杂
需求的方法。
——克莱顿·克里斯坦森
需求洞察将“场景-动机-任务-期望-痛点”整合起来
用户任务源于其在某个场景下的购买动机（目的）。执行用户任务是一个旅程，有期望也有阻碍，期望和阻碍
的差距形成了用户痛点，痛点是用户需求未被满足的表现。
用户购买是获得的利益（满足期望、减轻阻碍）与付出的成本（时间、金钱、精力）之间的权衡。
需求分析与需求洞察
客户利益
场景
客户动机
客户原声
客户任务
客户需求
需求洞察
内部需求
产品需求
标准约束
•质量属性
•DFX
 •功能需求
•非功能需求
•书面标准
需求分析
设计需求
•事实标准
架构设计
硬件需求
系统规格
软件需求
术语定义描述
客户原声最原始的、未经加工的客户原话（VOC）。VOC可以是客户对产品
的意见、建议或表扬，也可以是客户对自己的问题、痛点或期望的表
述，还可以是客户对产品功能特性的要求。VOC来自客户调研、客
户投诉、客户支持等多个渠道。
客户自然语言（所说、所
看、说做、一整天）
客户需求客户需求是个人或组织在某个场景下完成某个任务时未被实现的期待
或遇到的问题。客户需求站在客户视角表述。客户需求来自对客户原
声的洞察。
用户任务旅程地图：角色、
动机、场景、任务、旅程、
期待、阻碍、痛点
产品需求产品必须具备的功能或特性。产品需求站在产品视角表述。产品需求
按类型可以分为功能需求、非功能需求和约束。产品需求按来源可以
分为外部需求和DFX需求。产品需求应尽量不限制产品使用的技术。
产品是…（特性）
产品能...（功能）
区别三个术语
结论：需求分析vs需求洞察
需求分析关注需求的…需求洞察关注需求的…
内容What（是什么、多大程度）——更明确
How（如何解决）
Why（为什么、原因和目的）——更深刻
How（如何进步、如何愉悦地完成任务）
输入客户反馈、竞品文档场景、旅程（任务、行为、动机）、障碍
输出产品需求：功能清单、非功能需求客户需求：客户利益、任务、痛点
用途功能可落地、需求满足发现机会、识别真伪、弄清本质、优先级排序
需求洞察与锐捷原118中需求分析有何区别
需求洞察和锐捷原118需求分析存在关联性，都是围绕着客户需求为起点，也采用
了相似的场景、任务、四度评分法等，但是有几个核心差异：
• 需求洞察是在需求分析的基础上，从新产品开发的客户、竞争、技术的整体视角，
进行需求的全面洞察，采用“本质三问”洞察思维，强调洞察需求的“本质”和
“破界”。
• 需求洞察贯穿模糊前端活动，不仅能回答洞察结果的What，还能回答Why。将
需求真伪、KANO、0-1-3、项目支柱、价值主张等主要概念全部贯穿起来，形
成完整工具链。
内容
1、需求分析与需求洞察的区别
2、需求洞察的四大目标
3、需求洞察的步骤和工具
需求洞察要达到四个目标
1、从需求库中拿出来的需求，不好判断是不是真实的需
求：有可能做了半天发现是伪需求。
2、是真实需求，但不好判断是不是一个有价值的需求：
需求也能说出点一二三，但是总感觉有点鸡肋，做了客户
也不一定会买单。
3、是有价值的需求，应该满足到什么程度才是最优： 我
们认为的期望型、魅力型需求开发的挺辛苦，也挺有技术
含量，也做出和友商的差异了，但是客户就是不感兴趣或
者只是叫好不叫座。
4、有些需求只是客户针对他自己的任务提的一种解法，
而我们却抱着这个解法当成需求在往下拆解功能，做了半
⚫ 真伪：识别这是不是一个正确的需求
⚫ 本质：清晰定义出客户本质要解决的问题，
从而能给出这个需求解到什么程度最合适，
进一步打破现有或常规解决方案的边界，
找到更好地满足客户的方法
⚫ 分类：能够对客户需求进行精确的KANO
分类，尤其对客户长期不变、不断追求的
期望型需求的准确定义和判断
⚫ 优先级：能够对客户需求的迫切/重要程度
做出准确判断，为需求的优先级排序以及
产品创新提供依据 
天也只是在客户认知范围内解题，无法做到超过客户预期？
需求洞察的四大目标
从需求分析到需求洞察
一、伪需求
伪需求一种是用户或市场表面声称需要，但实际无法转化为有效价值的需
求；还有一种来自于产品经理的主观臆断——觉得用户需要。这类需求往往源
于对问题的错误理解，或解决方案与真实需求不匹配。
特征：看似合理但缺乏真实场景支撑，解决后用户不付费，或者使用率很
低，或者仍旧不满意，更没有转推荐等行为。
判断需求真伪的常见方法：机会视角识别真伪
Meta的真伪需求打分表
小米谷仓学院的“五层过滤”法
需求真伪，终极看是机会判断和机会选择的问题。
但首先，要解决需求识别环节对用户提出的要求的轻信以及产品经理的认知偏差问题。
即：“产品经理所说”不等于“用户所说”，
“用户所说”不等于“用户所做”，
“用户说要”不等于“用户真要”。
因此，判断伪需求的标准是：没有对应“用户任务”的需求就是伪需求。
类型表现示例原因
1、高频低价值需
求
用户常提及，但解决后对核心
功能、核心体验无提升
扫地机器人添加“宠物对话翻译”功能，用
户调研时反馈热烈，但实际使用率不足5%
（家庭用户核心需求是清洁效率）
误将客户有兴趣、常提及的需
求等同于真实使用时的核心需
求
2、解决方案与问
题错配
用户表达的是“方案”，而非
“问题”或者“任务”
工厂要求“给搬运机器人增加激光雷达”，
更优方案是“优化路径算法+低成本传感器”
（真实问题是减少货物搬运时的碰撞）
需求挖掘停留在解法层，缺乏
任务层洞察（客户需求受限于
技术认知提不出最优解法）
3、主观表达与客
观行为不一致
用户调研中声称“需要”某个
高端功能，但实际不付费/不
使用
医疗机构采购消毒机器人时强烈要求"手术
室级灭菌标准"，实际使用中80%时间仅用
于普通病房基础消毒
缺乏本质洞察：需求挖掘停留
在表达层。忽略了采购者与使
用者需求的不一致。
4、小众需求误判
为普适需求
少数特殊场景的用户强烈反馈，
但无法普适化到更多客户
为核电站巡检机器人开发的"耐强辐射涂层"，
推广到普通制造业客户时遭遇成本抗性
认知偏差：过度依赖首个标杆
客户的需求样本
5、技术驱动但缺
乏场景验证
某个技术可实现某个新功能，
就将其应用于某个场景，却发
现并非并非真实使用场景
智能冰箱加入“语音控制开冰箱”功能，用
户实际场景中用的很少
自嗨式创新：以为是魅力型需
求
6、市场阶段误判需求真实但市场未成熟，过早
投入导致失败
1993年苹果上市NewtonMessagePad
 2010年苹果上市iPad
技术周期误判：技术成熟度与
市场接受度存在时间差
伪需求的六种表现形式
1、用户认知盲区
  用户难以准确描述深层需求；
  用户基于自身经验提出片面方案；
2、 决策盲区
  追逐风口而忽略能力匹配
追逐技术先进而忽略需求阶段
  过度依赖少数用户或非典型场景。
将相关性误认为因果性。
3、 市场噪音
  • 跟随友商，误认为主流；
  • 媒体或资本炒作概念。
伪需求的本质是认知偏差，识别伪需求与四大方法均有关
1、还原到客户的场景和任务上
观察其行而非听其言：分析用户任务。用户提
出方案其实是为了完成…该方案可以替代为…
 2、穿透表层，追问本质
5Why、本质三问（T型追问）、系统思维
3、通过需求分类，警惕伪需求高发区
警惕魅力需求：伪需求高发区
4、识别客户的优先级
    “伪”不同于“假”，更多的是某个客群在
某个利益下的权衡取舍。因此，要识别不同客
群的不同优先级，避免简单的“一刀切”。
二、本质思考
1、需求洞察必须启动本质思考
2、本质思考的起点
1、用户的显性任务。
2、将现实问题的How转换成澄清后的问题What。
3、本质思考要形成闭环
需求根因要与问题还原、场景还原、任务还原、决策利益相结合，并保持动态进化
What else？
从T型追问到系统思维
What？
What else？
Why？
What？
What if？
What？What if？What if？
What else？
Why？
Why？
Really？
MECE
看全
二八法
则关键
Really？
隐含假设
求真
Why？系统目的
破界
根因分析
本质
反
馈
进
化
澄清问题
看全事物
上问目的
下问根因
再问假设
认知进化
现实问题
（How）
澄清问题
（What）
三、需求分类
需求分类用于：
1、优先级决策：识别哪些需求能满足“80%用户的80%需求”；
2、资源配置：避免将资源投入“镀金”类需求；确保有限资源投入实现最大产出
3、统一认知：用分类框架减少“销售说重要，技术不这么看”的冲突；
4、构建产品演进路线：找到魅力-期望-必备之间演化路线，明确需要长期坚持的
“越来越”需求。
需求分类是需求的结构化认知。核心目的是找准关键需求，从而找准关键解法。
常用的需求分类（国内外）
1、马斯洛的需求分类：生理、安全、社交、尊重、自我实现
2、JTBD的任务分类（与价值主张画布相同）：功能型、情感型、社会型
3、梁宁的真需求价值分类：功能价值、情绪价值、投资价值
4、Kano的需求分类：魅力型、期望型、必备型、无兴趣型、反向型
5、尼尔森需求分类：功能、可靠、易用、愉悦（Jakob Nielsen《UsabilityEngineering》）
6、MoSCoW分类（项目管理的优先级排序法）：必须有Must Have、应该有Should have、可以
有Could have、不会有Won’t have
 7、2B需求分类：经济、功能、合规、政治（Frederick E. Webster & Yoram Wind
《Organizational Buying Behavior》）
分类体现了人类对事物的归纳和认知，比识别真伪难度更大。
即便采用一种主要分类方式，仍然要意识到商业世界的复杂性、动态性，灵活运用多种分类。
四、需求优先级排序
优先级排序的本质是：在有限资源（时间、人力、资金）下，选择能带来最高
利益（比如ROI）的需求组合。
客户自己的优先级排序，与供应商的优先级排序，由于认知、任务、利益的
不同，往往不匹配。弄清楚客户的优先级排序是“以客户为中心”的基本要求。
因为分类的主要目的就是优先级排序，所以很多分类方法也用于优先级排序。
复杂项目和高重要度项目的优先级排序，牵涉战略选择和决策取舍。所以，
需要根据多个维度的判断才能做出选择。
洞察形容词本质关键矛盾
1. 伪需求筛子过滤无效需求用户表达与真实行为的偏差
2. 需求本质放大镜和显微镜穿透表层、挖掘真实关键解决方案与问题根源的失焦
3. 需求分类贴标签需求结构化认知分类维度与商业目标的错配
4. 优先级称重资源分配的最优化有限资源与无限需求的冲突
逻辑链：识别伪需求→挖掘需求本质→对需求分类→排出优先级
需求洞察四大目标的逻辑链
研讨：收获和理解
对以下内容有什么收获和理解：
（1）客户原声、客户需求、产品需求三个核心概念。
（2）需求洞察的四大目标
（3）如何识别伪需求
时间：研讨15分钟，分享10分钟。
分享人：三组。每组选出代表
休息
内容
1、需求分析与需求洞察的区别
2、需求洞察的四大目标
3、需求洞察的步骤和工具
需求洞察的三大步骤、五大工具
从需求分析到需求洞察
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
术语内涵来源和赋值
客户物品、系统或服务交易的购买方。客户可以是一个组织、一个群体（例
如家庭）或一个自然人。
客户描述、决策链洞察，0-5分
用户在特定场景下，为了完成特定任务，使用某种物品、系统或服务的人。客户转化而来
场景用户完成任务时所处于的情境，包括环境、资源、条件、关系等场景描述、客户故事
动机客户任务的驱动力。动机变了，任务、旅程、利益、痛点都随之变化。客户KPI、客户利益
痛点用户在尝试完成任务的过程中无法达成的期望客户利益差距、期望差距、体验差距
利益用户期待完成任务获得的全部收益。利益包括但不局限于KPI。利益变
了，任务、旅程、利益、痛点都随之变化。
决策链洞察
收益评价痛点解决方案给用户带来的收益增量的评价高、中、低
需求重要度评价对于用户、任务、利益、痛点的重要度的评价高、中、低
需求满足度评价评价产品或解决方案帮助用户完成任务、达成期望、解决痛点的程度不满足、满足、过满足
KANO分类基于KANO模型的一种客户需求分类方式必备、期望、魅力、无兴趣、反向
客户需求优先级对客户需求的优先顺序进行的排序，确保有限资源投入实现最大产出必做、高、中、低
0-1-3分类基于0-1-3模型的一种产品需求分类方式0短板、长板、魅力
产品需求选择基于产品需求优先级，通过战略、竞争等更多维度进行需求选择，确保
有限资源投入实现最大产出
必做、高->低
需求洞察的关键术语
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
整合“场景-动机-任务-期望-痛点”的工具：用户任务旅程图
用户任务源于其在某个场景下的购买动机（目的）。执行用户任务是一个旅程，有期望也有阻碍，期望和阻碍
的差距形成了用户痛点，痛点是用户需求未被满足的表现。
用户购买是获得的利益（满足期望、减轻阻碍）与付出的成本（时间、金钱、精力）之间的权衡。
用户任务旅程图：呈现用户出于某个动机，在某个场景下，去完成某个任务的旅程
中，用户的预期、经历的步骤、面临的阻碍、付出的成本等。
明确用户角色
明确任务和动机
任务的场景是什么
任务结果的衡量标准是什么
任务的旅程步骤
旅程步骤的预期是什么
用户面临的阻碍是什么
旅程步骤的成本代价是什么
本质三问
系统思考
贯穿始终
旅程图输出：用户痛点
洞察伪需求
如果一个需求无法还原到用户任务旅程中，也与客户利益无关联，则是伪需求。
用户任务旅程图
第一步：明确用户角色
主角：用户任务旅程图要基于一个明确的主要用户角色来分析，聚焦主角的任务旅程，在整个任务
旅程中主角应保持稳定不变。主角是产品的主要服务对象，产品价值的主要受益者。
选择哪个用户角色作为主角？一看分析目的，聚焦于哪个环节。二看全生命周期旅程，从提出需求、
获得供应商信息、确定规格、到采购决策，谁最重要？
客户
决策者
购买者
使用者
维护者
影响者
多角色：如果多个角色都是同等重要的产品受益者，应从多个角色的视角分别展开多个用户任务旅程图。
1、用JTBD框架挖掘用户任务：某个角色
在某个场景中所追求的进步是什么？
2、根据动机和场景理解任务：用户基于
某个动机在某个场景下会如何完成任务
挖掘用户任务是用户任务旅程图最关键的
一步，这一步错误后面都错了。
“用户任务”公式：xx用户群体购买xx产品实际上是为了xx
第二步：明确任务用户任务旅程图
用问题清单挖掘任务
• 用户/客户为什么会购买/消费本产品/服务？使用产品/服务的目的是什么？想要解决他的
什么问题？
• 用户/客户一般会在什么场景下用这个产品/服务？这些场景有什么共性么？为什么会在
这个场景下使用？
• 除了功能性任务，还有没有情感性任务？
• 或者有没有情感性任务过度了，反而功能性任务未满足？
• 除了满足客户的任务，是否考虑过终端用户的任务？
• 这些目的，是否可以合并分类？在这个大的类别下基于MECE，还有没有别的可能的任
务？
What else？
用T型追问（系统思维）挖掘任务
What？
What else？
Why？
What？
What if？
What？What if？What if？
What else？
Why？
Why？
Really？
MECE
看全
二八法
则关键
Really？
隐含假设
求真
Why？系统目的
破界
根因分析
本质
反
馈
进
化
澄清问题
看全事物
上问目的
下问根因
再问假设
认知进化
现实问题
（How）
澄清问题
（What）
关键判断法-选择关键任务：选择系统最重要的点、T型追问的恰当层级作为本次展
开分析的任务（恰当的层级是指最有可能发力的层级），不要错过有可能颠覆现有
旅程的机会。
复杂系统
系统目的
用户需要一把电钻
关键
为什么？
为什么？
为什么？
墙上的洞
挂在墙上的画
更美观的客厅
为什么？ 愉悦的心理感受
问题的不同层级会产生完全不同的解法
每个点都是一个任务，是复杂系统的子系统
关键判断法-明确关键任务：二八法则
输入比重
80%
 20%
 20%
 80%
输出比重
帕累托定律：总结果的80%是由占总资源的20%的输入所导致的
难题的系统边界在 …… 【场景/任务/指标/领域】，
难题描述如何能够【消除/反转动词】【难题/麻烦】，达成【期待】？
关键难题只有解决了这个【难题】，才能实现（重大目标）
关键判断法-明确关键任务：句式法
序号场景动词麻烦期待投票
（每人两票）
1
 2
 3
 4
出行台风准时扭转
关键判断法-明确关键任务：投票法
关键判断法-明确关键任务：判断标准
• 这个事情做不好，其他事情也无法做好
• 这个事情做好了，其他麻烦会减少
18650
电池
BMS
电池管
理系统
示例：特斯拉早期如何找到电动车降低成本的关键要素
用户任务旅程图
第三步：还原任务的场景和动机
场景：用户完成任务时所处于的特定情境。场景可以看作是一个“人--人交互、
人--物交互、物--物交互”的故事。
场景六要素：用户、时间、地点、动机、任务、环境。
场景是用户任务的土壤，它决定了用户任务、旅程和阻碍。切不可脱离场景分析
用户任务。
场景可以采用文字或故事板描述。
角色：网络管理员；任务：排查网络故障
场景一：某员工报告视频会议卡顿
场景二：办公楼某层断网
场景三：某领导投诉最近一段时间网络差
示例：Z世代用户的动机
孤独疗愈
懒宅文化
音乐游戏
极限运动
内在感受
圈层文化
文化自信
民族自信
爱豆偶像
个体
趣
我开心就好
嗨
圈层文化
颜值主义
美
颜值即正义
秀
自我表达
破产三坑
健康饮食
曲线展示
种草拔草
弹幕文化
社交动态
社畜自嘲
外在表达
群体
示例：游戏用户的动机
第四步：识别用户对于任务结果的衡量标准
“客户想要完成任务，但同时他们还想要以更快的速度、更高的质量或更低的成本完成某项任务。
为了清楚地知道"更快"、"更好"意味着什么，企业必须能够从客户那里获取一系列的衡量标准，
即客户是如何定义他们想如何完成任务,以及如何才算做到极致的。”
 Ulwick《What customers want》
角色：网络管理员；任务：排查网络故障
结果衡量标准一：查出网络故障的准确发生时刻
结果衡量标准二：查出网络故障的影响面
结果衡量标准三：查出网络故障的直接原因
结果衡量标准四：查出网络故障的根本原因
结果衡量标准五：尽可能缩短花费时间，最长不能超过10分钟
用户任务旅程图
用户任务指标：用户完成某项待办
任务时最在乎的方面，是这类用户
选择某产品而不是其它产品完成该
任务的度量指标。
用户任务旅程图
打车上班
善于从
多个视
角看旅
程
第五步：还原任务的旅程步骤
梳理用户是如何一步一步完成任务的
叫车阶段
打开
APP
选择
终点
等待
叫车
叫车
成功
出门
等车阶段
乘车阶段
最后100米
确认上
车点
Acquisition
获客
Activation
激活
寻找
车辆
乘坐
Retention
到达
公司
步行至
办公楼
Revenue
乘坐
电梯
刷卡
进门
到工位
Refer
留存
研发
生产
变现
销售
推荐
交货
知晓
熟悉
试用
服务
使用
忠诚
第六步：识别用户对于旅程步骤的预期
示例：博世圆锯用户的预期（部分）:
 •最小化圆锯开始工作时后坐力的值。
•提高刀片一开始就能精确地沿切割线切割的可能性。
•最小化切割时看不到切割线的时间。
•最小化圆锯在切割面上保持平稳所需要的压力值。
•最小化刀片护罩挂住切割材料的可能性。
•最小化切割过程中线缆卷入切割路径的可能性。
•最小化线缆(电源插头)被切割材料卡住的频率。
•最小化检查刀片位置的频率。
•增加一个刀片可以切割的次数。
•最小化桌面不平整对切割带来的影响程度。
•最小化圆锯产生的灰尘和碎屑的数量。
用户对于旅程中每个阶段的每个步骤，都有预期。如果预期没有达成，就会产生不满。
“对于大部分的用户任务来说，即使一些很
琐碎的任务，通常也会包含50-150个目标成
果。企业必须从客户那里获得全部关于目标
成果的信息，因为你永远不知道你的产品或
服务目前还不满足哪些成果。”
 Ulwick《What customers want》
用户任务旅程图
第七步：识别用户面临的阻碍
阻碍是指阻碍用户完成任务、达成预期的客观情况，阻碍导致了成本和痛点。
阻碍包括外因和内因。
外因如：恶劣环境、资源限制、规定限制等，
内因如：能力不足、工具缺失、知识不足等。
角色：网络管理员；任务：排查网络故障
阻碍一：时有时无的故障难以复现
阻碍二：报障人不知道如何查自己的IP和MAC
阻碍三：不同的数据在不同的系统上
阻碍四：网管经验和能力参差不齐
阻碍五：应用部门不配合
用户任务旅程图
阻碍信号1：缺陷
觉得不够好的地方，如抱怨
阻碍信号2：瓶颈
难以进行的地方，如放弃
阻碍信号3：冗余
多次操作的地方，如重复操作
第八步：分析旅程步骤的成本
入网前入网中入网后
公司网管完成业务终端入网
收到同事
申请信息
查看终
端类型
查看IP地址及
记录EXCEL
分配未使用
的IP地址
发送IP地址
给同事
看到设备上
线提醒
1s 2min 3min 3min 1~2D 2min 2min
打电话指导
同事操作
15min
联系网络部门确定接入交换机
的端口、Vlan、访问策略等
一个终端上线，需要1-2天
技术人员前
往现场处理
前往客
户现场
定位具
体问题
返回
公司
再次前
往现场
动手处
理问题
接到客
户的故
障电话
接到客
户的故
障电话
花费150元花费150元
成本包括时间成本和金钱成本。
当用户在旅程中付出的成本超出用户预期时，用户就会产生痛点。
工程商去县城排障
用户任务旅程图
用户任务旅程图
第九步：识别用户的痛点
痛点：用户完成任务的过程中，由于阻碍的存在，无法达成任务结果、无法实现过程预期、
出现超出预期的成本。
角色：网络管理员；任务：排查网络故障
无法达成的任务结果->
无法实现的过程预期->
超出预期的成本->
痛点一：由于无法复现网络故障，从而无法排查出故障原因
痛点二：由于报障人不知道如何查IP和MAC，无法定位报障人的位置
痛点三：前后花了两个小时
关键判断法-明确关键任务：句式法
难题的系统边界
难题描述
在 …… 【场景/任务/指标/领域】，
要【消除/反转动词】什么【难题/麻烦】
第十步：输出客户需求
用户任务旅程图
客户需求句式：
【谁】在 …… 【场景/任务/指标/领域】，要解决什么【问题】，以实现【期待】
对于2B洞察，不能只做单环节旅程、单角色旅程：
1、最容易想到的是“用”的旅程：完成任务的过程和结果。拆细、拆全、拆透。
2、还应洞察“买”的旅程，包括“买”前的寻找供应商旅程、“买”中的招投
标旅程、“买”后的服务乃至全生命周期旅程，才能洞察清楚客户购买的逻辑。
3、进一步洞察决策环节：从决策发起到决策过程到决策绩效评价全决策链，洞
察“买”背后的“因”。
旅程图的运用要点
案例分享：用户任务旅程图
内容：个人智能哑终端入网。
分享者：周行
时间：20分钟     
休息
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
客户动机和客户利益
区别客户动机客户利益
定义一驱动用户采取行动的内在或外在需求，是用户与
系统交互的原因。ISO9241 210 : 2019《以人
为中心的交互系统设计》
决策过程中各角色期望获得的显性/
隐性价值总和（Porter价值链理论）
定义二用户通过‘雇佣’产品来完成特定任务的深层原
因，与场景强绑定（JTBD）
组织采购决策中经济/政治/情感等收
益的平衡（Kotler营销管理）
本质客户任务的单点触发机制（行为驱动力）多维价值标准（决策系统）
层级操作层决策层
特点单一角色动力多角色博弈
举例医院放射科医生"提升诊断准确率"院长关注"降低设备生命周期成本"
动机是任务和需求的起始点，但不一定是最终决策的关键变量
客户动机和客户利益
场景触发
组织KPI
动机激活
利益评估
任务执行
决策链博弈
采购需求产生阶段：财务部成本控制动机（动机）→ 触发采购流程（任务）→ 各角色利益诉求显性化（利益）
技术验证阶段：IT部门系统整合动机 → 技术白皮书评估 → 兼容性/安全性利益权重上升
最终决策阶段：CEO战略价值动机 → 综合ROI分析 → 组织级利益达成共识
客户利益洞察工具：外部供应链+内部决策链
客户决策关注点：客户利益的显性表达
客户原声（VOC）
客户需求：用户任务旅程
Jobs Journey
客户利益
Customer Benefit
供应链决策
supply chain
终端用户  零售商
储运商 供应商
生产商 材料商
决策链决策
Decision chain
提出需求 定义需求
描述规格询价采购
配置安装 操作学习
维护运营 绩效评价
示例：2B客户的内部采购决策链
问题识别
使用部门
明确需求
工程师
描述规格 寻找供应商
征求供应信息 选择供应商
工程师
采购经理
采购部门
决策者
正式订购
采购部门
不同部门和角色处于决策链的不同环节，决策关注点和影响力均不相同
绩效评价
管理部门
内部决策链分析四步
步骤1：绘制决策链，进行角色识别
• 绘制决策链：需求提出（使用部门）→ 技术评估（工程师）→ 预算审批（财务）→ 合同签订（法务）
→ 执行（采购部）
• 角色识别：发起者、使用者、技术评估者、财务审批者、最终决策者、守门人等。
步骤2：提炼决策关注点（客户利益）
决策关注点：通过客户访谈、同理心图等方法洞察各角色的核心诉求（如技术部门关注兼容性，财务部
门关注ROI）。还可以通过VOC聚类识别。
步骤3：进行影响力评分（参考标准）
• 指标：决策否决权（基于职位的正式权力，0-5分）、信息控制权（专家威信、人际关系、信息传递
等非正式权力，0-5分）、利益相关度（个人/部门受益程度，0-5分）。
• 公式：综合影响力 = （否决权×0.5 + 信息控制权×0.3 + 利益相关度×0.2）。
步骤4：生成角色-利益-影响力表
决策链分析的输出：角色-利益-影响力表
绘制决策链，进行角色识别
角色
提炼决策关注点
客户利益（客户决策关注点）
决策者
使用者
运维管理者
采购者
高性能、节省成本
易使用、高性能
5
进行影响力评分
影响力打分
4
高稳定，易维护
供应及时&可靠
3
 3
示例：角色-利益-影响力表
举例：医疗器械采购决策链分析
决策链：
临床医生采购委员会医院院长
发起者
角色-利益-影响力表：
技术/财务评估
• 临床医生：使用反馈，影响力3分；
• 采购主任：控制预算分配，影响力4分；
• 院长：最终审批权，影响力5分。
最终决策者
示例：高校采购在线教育平台决策链分析
1、决策链：
需求提出（教师）→ 技术评估（IT中心）→ 预算审批（财务处）→ 最终决策（分管副校长）。
2、角色识别、决策关注点、影响力评分：
• 教师（发起者）：基于教学效率提升提出需求，但无决策权。
• IT中心（影响者）：关注关注系统兼容性、数据安全，影响力评分4.2。
• 财务处（守门人）：关注成本控制，影响力评分3.8。
• 副校长（最终决策者）：关注综合效益、学校声誉，影响力评分5.0。
3、角色-利益-影响力表
示例：医院采购医疗影像设备决策链分析
1、决策链：
临床需求（放射科医生）→ 技术评估（设备科）→ 预算审批（采购办）→ 最终决策（院长办公会）。
2、角色、决策关注点、影响力评分：
• 放射科医生（影响者）：关注设备精度，影响力评分4.0。
• 设备科（技术评估者）：关注设备寿命与运维成本，影响力评分4.5。
• 采购办（守门人）：关注招投标流程、投标人资质，影响力评分3.5。
• 院长（最终决策者）：关注医院排名与合规风险，影响力评分5.0。
3、角色-利益-影响力表
示例：金融机构采购风控系统决策链分析
1、决策链：
需求提出（风控部）→ 技术验证（科技部）→ 合规审核（法律合规部）→ 预算批准（CFO）→ 最终决策
（CEO）。
2、角色、决策关注点、影响力评分：
• 风控部（发起者）：关注提高风险识别能力，影响力评分4.0。
• 科技部（影响者）：关注系统与现有IT架构的整合难度，影响力评分4.8。
• 法律合规部（守门人）：关注金融监管要求，影响力评分4.5。
• CFO（财务决策者）：关注采购成本，影响力评分4.2。
• CEO（最终决策者）：关注风险与收益的平衡，影响力评分5.0。
3、角色-利益-影响力表
示例：外部供应链和决策链（化肥）
10%
农场主
20%
地区经销商
70%
全国分销商
化肥供应商
农业顾问
最终用户的决策因素：经济作物养分需求
细分变量一：采购影响者
• 70%的农场主通过全国经销商采购
• 20%直接向分销商采购。
• 10%的采购决策受农业顾问影响。
细分变量二：农场主规模影响差异化需求
大规模农场重视高效施肥方案
小型农场重视成本控制
示例：采购链和外部决策链（IT设备）
终端企业
30%
 IT顾问
60%
企业采购部
10%
招标平台
IT供应商
关键洞察：
工程顾问影响30%的采购决策，但公司在此环节的渗透不足。
细分策略：
• 与IT顾问建立长期合作。
• 优化招标文件，突出技术优势。
案例：如何将机器人应用于4S店？
+
某公司主营智能硬件，智能机器人时代，存在大机会。同时，该公司的一个客户资源是汽车4S
店。公司确定了一个新产品目标：探索智能机器人应用于4S店的场景。
产品经理的初始问题：如何将智能机器人应用于4S店？
第一轮解决方案
公司的产品经理访谈了
4S店员工以及现场。很
快拿出了产品概念：
4S店展厅接待
产品价值点：
1、形象代言
2、人脸扫描
3、客户再来店自动识别
方案被否决
But，4S店决策层也
迅速给出了反馈：
不需要！
因为：
接待是促成销售的关键环
节，客户更喜欢与人打交
道。机器人接待的体验并不
好，不适合。
4S店展厅经理是智能机
器人的关键决策角色
发起人和发起人任务
4S店老板拥有采购智能机
器人的决策权：真需求
决策人和决策者利益
决策者的关注点是降本增效（客户利益）
机器人不能替
代接待工作
为什么？
4S店不希望接
待工作被替代
为什么？
4S店老板采购机
器人的目的不是
客户满意度
什么是可能
替代的工作？
什么是期
待被替代
的工作？
4S店员不喜欢、
在抱怨的工作
为什么？
很繁琐
很劳累
4S店员希望借
助机器人优势
的工作
为什么？
什么是关
键目的？
老板希望服务
标准化
机器人的长
项是不劳累、
擅长记忆、
智能识别
本质是什么？
减少
抱怨
手段
本质是什么？
增效
提高
效率
本质是什么？
为什么？
老板的目标
是减员增效
商业
价值
目标
降本
基于这个核心利益重新进行同理心图和旅程图分析，
找到任务痛点
新员工要花1-4
个月培训
准备
销售每天主要工作接待客
户，电话邀约，交车
如果半年还没有业
绩达标就会被淘汰
汽车销售离职
率非常高
办交车
背车的性
能参数
串业务部门
学习公司业
务程序
电话邀
约到店
客户对
应演练
串部门
办手续
接待
客户
陪客
户排
队
逼单
业务完不成下个
月就要走人了
参数熟
记有助
于提高
客户信
任度
Sunny
 4S店员工
交车这么花时间
还没效益！
财务部这些部
门就会刁难我
们这些新人
认识很多新
朋友很开心
公司的新
员工培训
很烦
交车不赚钱但
是为了客户满
意度没办法
业务压力
很大
打电话邀约
确定客户来
电很烦
大半天都忙
交车很郁闷
借助角色扮演，识别场景和旅程中的关键难题、关键痛点
拆解4S店的用户购买和消费旅程，找到关键要素
兴趣
接待
洽谈
兴趣
传播
展示
邀约
接待
讲解
试车
比较
交车
洽谈
车型
价格
服务
交车
验车
收款
装修
售后
售后
保养
活动
介绍
繁琐、耗时、抱怨多、
相对标准化
进一步拆解交车旅程
步骤
Steps
行为
Doing
想法
Thinking
感受
Feeling
验车
约客
户
4S店
检测
谈判
调车
开发票
证件收
集
签字
陪同
有问题的话我
该怎么说呢？
核对信息
排队
换车很麻烦，
可别出问题
会不会需要换
车呢？
要严谨
交保险
排队
沟通选择保
险公司
签字
合同金额，
时间可别出
问题
繁琐
累
紧
张
纸质信
息是否
准确？
换位思
考
忍
装饰
预约沟通
开单排队
签字
有没有顺利
排上？
怎么能二次销
售多卖点呢？
着急
焦虑
交车款
排队
签字
核对信息
交车仪式
能不能快点
了？
一天又过去了！都
没功夫忙别的！
高兴
累
郁闷
1
客户需求
2
 3
智能交车
智能培训
智能邀约
4 高端车型社会关系智能识别
智能交车产品需求
智能交车
客户
智能识别
车参数
智能记忆
付款开票智
能服务
保险
智能服务
验车
智能服务
装修
智能服务
客户
智能回访
总体逻辑：
用户任务旅程图+决策链+本质三问
用户任务旅程图+决策链+本质三问
问题定义
用户旅程
痛点
客户决策
关注点
思考逻辑
理解用户行为
用户旅程图
通过可视化用户生命周期各阶段，
识别接触点、体验及痛点。
挖掘需求动机
+
 JTBD
跳出产品功能视角，聚焦用户
“待办任务”。挖掘深层需求。
洞察真实痛点
=
用户任务旅程图
呈现用户基于什么动机和场景完成任务，
识别旅程中预期、阻碍、成本及痛点。
决策链
客户决策关注点对应的利益
呈现客户决策关注的维度以
及该维度反映的背后利益
动机对应的利益
支撑
本质三问
客户决策关注点能反映出决策链客户利益大小，用户旅程能反映出完成用户任务带来的客户利益大
客户利益
小，这两者是需求重要度的核心衡量指标。注意需求满足也是有代价的，不能忽视代价只谈收益。
What else？
从T型追问到系统思维
What？
What else？
Why？
What？
What if？
What？What if？What if？
What else？
Why？
Why？
Really？
MECE
看全
二八法
则关键
Really？
隐含假设
求真
Why？系统目的
破界
根因分析
本质
反
馈
进
化
澄清问题
看全事物
上问目的
下问根因
探究假设
认知进化
现实问题
（How）
澄清问题
（What）
需求洞察要善于多视角看问题
这个问题：
从技术上看，价值是？
从产品上看，价值是？
从公司看，价值是？
从行业看，价值是？
从社会看，价值是？
从国家看，价值是？
从全球看，价值是？
…
升
维
思
考
降
维
行
动
多视角看问题的工具：问题沙盘
系统
向外
基因 本因 原因 事实
过去
宏观
行业
公司
产品
当下
感受
期待 目标 蓝图 愿景
信念
初心
价值观
未来
时间
向内
检核：客户利益往往与外部宏观趋势变化相关
宏观
环境
行业
环境
客户利益
变化产生的根源
客户新利益
势：趋势，是即将来到的红利
示例：采用PEST检核决策利益
10倍速变化
政策因素
1940年，二战
新需求
旧要素
新指标：便利性、多功能 刀+工具 瑞士军刀
经济因素 1991年，航空业 新任务：航站楼内搬运箱子 箱子+轮子 新秀丽
社会因素
技术因素
2016年，Z世代
新群体：20岁左右新消费者
新指标：健康、口味、颜值 茶+奶盖
2016年，4G
新指标：商家更便宜更高效
的价值传递
新组合
喜茶
商品+内容 直播电商
工具：十倍速要素分析
PEST要素
政策
经济
社会
技术
10倍速变化
新利益
十倍速影响下，新需求对应的市场规模是否足够大、是否越来越大？
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
客户需求重要度/满足度识别原则
识别客户需求重要度/满足度的目的，是为了将洞察到的客户任务、客户利益、客户痛
点结构化到两个抽象维度上，以进一步站在客户角度看清：客户是如看待这些需求的、
是如何对这些需求进行排序的。
• 重要度：回到决策链和用户任务旅程进行判断，包括客户的重要度、任务的重要度、利
益的重要度，以及广度和频度。
• 客户利益：客户利益（决策关注点）是客户决策的根本标准，属于高维度诉求（比
如政策要求、能否为其开源、能否为其提效，损益的消除也是利益）。利益决定了
需求分类、排序和选择，重要度高，具有指向性、甚至否决性。
• 任务和痛点：用户任务包含了 “买”、“用”、“维”等旅程。可以以利益为标
准，用强度、广度、频度三个维度进行度量。
• 满足度：通过对用户任务的分析以及客观事实（含源概念分析的结果）的判断，比较容
易得出满足度的度量值。
基于三度进行需求重要度评分
三度
评分
强度（造成的影响）1-5分
说明
根据决策链洞察的客户利益和达成客户利益任务的痛点影响程度进行强度打分
基于某个细分行业
• 70%客户存在或者重要客户（战略级的）广度（5分）
广度（普遍性） 1-5分
频度（发生频率） 1-5分
需求重要度
• 50%以上客户存在（4分）
• 30%以上客户存在（3分）
• 非普适性或者当前无法判断（1~2分）
• 超高频：每天多次（5分）
• 高频：每天一次或每周多次（4分）
• 中频：每周一次或每月多次（3分）
• 中低频：每季一次或每年多次（2分）
• 低频：每年一次（1分）
注：
1、强度（评分5）且广度（评分4以上）的需求，频度可直接评分≥4分
2、客户决策关注点对应的需求如果没有落在任务旅程中，按5分计算；如果落在用户任务旅程中
则按上述规则计算。
强度*频度*广度 计算得出（低 < 36 <= 中 < 60 <= 高）  
客户利益（客户决策关注点）客户利益打分用户任务需求（痛点）
高性能5设计下一代智算数据中心网
络架构
训练场景下因为交换机性能限制，导
致训练效率偏低
节省成本5
易使用4
高稳定3
易维护3
供应及时&可靠4
 3、需求强度评分表
客户利益
（客户决策关注点）
强
严重影响任务开展（用户
任务可能无法达成）
中
影响业务开展（效率降低、成本增加、
可以容忍但要付出额外代价）
弱
不影响业务开展
（不方便、麻烦）
5分利益5 4 3
 4分利益4 3 2
 3分利益3 2
 2分利益2
 2、客户利益、用户任务、
痛点关联表
强度打分方法
角色客户利益（客户决策关注点）影响力打分
决策者高性能、节省成本5
使用者易使用、高性能4
运维管理者高稳定，易维护3
采购者供应及时&可靠3
 1、角色-利益-影响力表
需求满足度的判断标准
基于客户需求的期望，通过源概念拆解判断当前行业供应水平。
判断标准：
客户需求满足度 不满足
行业供应水平 无法达成
满足
过满足
可达成
超过当前要求
满足度评价包含态度层面（满意度），也包括行为过程层面以及任务结果层面（度量指标）的满足度。
描述赋值来源
重要度评价客户、任务都有重要度属性，重要的客户完成其
KPI的关键任务相关的需求就是重要的需求
决策链+用户任务旅程输出
满足度评价满足度是现状和预期的差距，是一个比较的结果决策链+用户任务旅程输出
KANO分类基于KANO模型的需求分类
优先级基于KANO分类的优先级排序
第一个步骤完成后，需求对应的客户利益、重要度、满足度完成了分析，其中的重要度、
满足度将作为第二步骤的输入。
到此，完成了对需求真伪的识别，对需求本质的探索，需求洞察目标达成2/4。
第一个步骤完成后
研讨：收获和理解
对以下内容有什么收获和理解：
（1）对需求洞察关键术语的理解。
（2）对用户任务旅程图的理解。
（3）对客户利益、决策链的理解。
时间：研讨15分钟，分享10分钟。
分享人：三组。每组选出代表
休息
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
卡诺模型：功能完备度-客户满意度模型
⚫传统质量观念认为，客户满意度取决于产品
功能多少。功能越多越满意、功能越少越不满
意。
⚫卡诺模型认为，产品配置什么功能取决于客
户期望的类型。有的功能是魅力属性，有的是
期望属性，有的是必备属性。
必备需求期望需求魅力需求
客户影响实现（Fulfil）满足（Satisfy）惊喜（Delight）
营销解读能否上桌
（Table Stakes）
竞争基础
（Basis of Competition）
独特特性
（Unique Feature）
商业决策必备（Must-Be）增强（Increase）创造（Invent）
增长模型向上渐进式线性增长指数型增长
卡诺模型的运用价值
KANO模型与满足度的关系
满足度态度产品属性
未满足投诉、不使用必备
满意度下降期望
无感魅力
满足无感必备
满意度提升期望
惊喜魅力
KANO模型是从用户态度层面（满意度）来辨识产品属性的满足度。
KANO模型与产品五个圈层的关系
未意识到的
服务 解决方案
对属性的期望
质量
设计
性能
品牌
包装
核心利益
潜在层
延伸层
期望层
有形层
核心层
魅力
期望
必备
• 必备、期望、魅力都必须
围绕核心利益
• 期望是对某些必备的期望，
并非所有必备都有期望
• 魅力会转化为期望、期望
也会转化为必备
KANO模型难题一：用户认知偏差
用户认知困境：用户只能对自己用过的产品做出准确回答，
对于自己未用过的产品，回答结果会很随意，带来巨大偏差。
而产品经理恰恰希望知道：用户对我们拟使用的功能的想法。
KANO模型难题二：问卷偏差
Chris Chapman
 Kano 模型之所以受欢迎，是因为它承诺提供非常有价值的东西：以问
卷调查中仅两个问题的成本，让人们一窥产品的未来成功。Kano 问卷很有
吸引力，但答案很可能是错误的。 
——Google研究员Chapman和Callegaro
 MIT 研究认为：KANO的单一问卷难以覆盖2B场景下的多维需求。同时，
2B企业还存在样本量不足的问题。因此，2B企业不适合通过问卷的方式
获得Kano分类。
Mario Callegaro
如果苹果iPad采用更高性能的LX电缆线，你会有什么感受？
人们可以非常理性地回答“是”,也可以根据心情、调查顺序或调查时间选择任何答案:
 ☒:我喜欢它——是的，因为它更快
☒:我希望如此——是的，因为苹果一直在做这样的改变
☒:我保持中立——是的，因为我能看到它的正反两面
☒:我可以忍受——是的，因为我可以买新的电缆了
☒:我不喜欢——是的，因为总的来说，我不想换来换去
这些项目并不相互排斥，因此卡诺评分并不合理
举例：Kano问卷问题设计带来的回答随意性
通用的Kano问卷，问题不是互斥的，用户很可能五个选项都同意。另外，
用户第一次填写的答案可能与第二次填写的答案截然不同。
Chris Chapman
 Kano 模型是在成熟、耐用的消费品（考虑烤箱、沙发、手表和汽车）
的背景下构思的。此类商品的功能变化缓慢，消费者可以很好地理解。在
这些类别中，实质性的新功能很少见，但当它们出现时，消费者相对容易
理解和判断它们。Kano模型的分类可能非常适合此类商品。
然而，对于快速迭代、提供全新功能或在体验之前难以理解或判断其
功能的产品，Kano 类别非常值得怀疑。包括高科技产品。
比如，科技产品中的三个常见性能：更快的处理器速度、更高质量的
图像和更长的电池寿命。这三个性能，既是必备的，也是期望的，还有可
能是魅力的。而且，高科技产品的性能提高速度很快，导致在消费者心中，
新产品性能增强既是意料之中的，也是必不可少的。
Mario Callegaro
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
通过重要度-满足度矩阵识别KANO分类
②
满
足
度
④
 ③
重要度
①
 ①客户满足度中、低，重要度中高：未满足的需求，
极大的机会。对应的是期望型需求。
②客户满足度高，重要度高：已满足的需求，上牌
桌（进入市场）必须要有的，但仅做它机会有限。
对应的是必备需求。
个没有KANO需求对应。
③满足度中，重要度中：刚好满足，机会不大。这
④满足度中，重要度中低：过满足-存在低端颠覆
的机会。
通过重要度-满足度矩阵判断KANO需求分类
必备1：需求重要度高，性能过剩。是必备需求，
但要避免镀金（刚好满足即可）。
必备2：典型的必备。
期望1：典型的期望。优先级更高。
期望2：期望需求，优先级低于期望1.
魅力：魅力需求。选择时要慎重。根据趋势判断
是否可以转化为期望，如果是，就是明天属性。
变化态（准必备）：当下是魅力（某个人群）或
者期望（另一个人群）。随着大众化，重要度提
升，变成必备。
变化态（可能消失）：或者属于过度满足、或者
属于重要度低，两者其中之一。
过时：果断放弃。
根据重要度、满足度评分，将客户需求以点状形式放在矩阵中。
本图：左上角四个框，建议慎重选择或放弃。
示例：金融中小网络的重要度-满足度矩阵
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
基于kano的任何一个判断都不是静态的，它们会随着时间的推移而变化。
客户现在对某些产品属性的感受并不是他们将来的感受。随着时间的推移，有
魅力的功能会变成期望功能和必备功能。这直接导致：
1、老产品的Kano分类结论无法用于新产品（比如，老市场-新产品）。
2、产品不变、客户群变（老产品-新市场），Kano分类也需要重新做。
KANO分类的动态性
KANO分类动态性的原因
KANO分类是由客户需求的重要度和满足度决定的。
随着时间变化，客户需求的重要度和满足度变化，KANO分类随之变化。
客户需求重要度变化，导致KANO属性变化
客户需求重要度提升
变化
路径
无差异
魅力
必备
期望
客户需求重要度降低
举例：- 手机支付，最早是无差异，之后是魅力，现在是必备。
PESTLE变化带来心
理、行为、决策关
注点变化，导致客
户判断需求重要度
的排序提升或降低。- 开车时听广播，早期是必备，现在是无差异。
策略1：关注客户未来而不是当下的需求，为客户未来的需求设计产品。
策略2：寻找长期稳定不变的客户需求作为主攻赛道。
为客户未来的需求设计产品
打造爆品时，我们首先考量的第一要素是，产品是否具备“明天属性”。
什么是“明天属性”？就是给用户提供代表先进趋势的、令他们向往的全新
体验，而且这种体验是用户一旦用过就不想放手的。
——《小米创业思考》
既要具备明天属性，又要识别那些实际上是伪需求的魅力需求，这是产品经理洞察力的关键。
客户需求满足度变化，导致KANO属性变化
客户期望值下降、满足度提升
变化
路径
举例：
期望
必备
客户期望值提升、满足度下降
技术进步带来成本降低和功能普及，使得客户期望值
降低到大众水准，满足度提升，客户对该属性的认知
从期望转为必备。
技术突破使不可能变为可能，带来客户期望值提升，
客户满足度降低，点燃对某些必备属性的期望。- 使用手机清晰拍摄夜景/远景/微距：在功能机时代是期望需求，现在是必备需求- 更容易地倒车泊车：几年前是必备需求（有倒车影像即可满足），现在变为期望需求（全自动泊车才可满足需求）
策略1：关注技术成熟度趋势，避免成为一上市就落后的产品
策略2：避免把“产品不够好”误判为“客户没有需求”而错失机会
成功需求的KANO变化规律
  路径：无差异→ 魅力属性→ 期
望属性→ 必备属性
  案例：包装的“可回收材料”属性
从2003年的魅力属性进化为2009
年的期望属性。
  驱动力：市场竞争推动属性大众
化，客户习惯的养成（如环保意识
提升），技术成熟降低成本。
  时间跨度：通常需要数年（5年
以上）。
哪些客户需求会指引产品走向成功？
昙花一现需求的KANO变化规律
  路径：无差异 → 期望属性 → 回
归无差异
  案例："传达产品系列"属性从
2003年的期望属性退化为2009年
的无差异属性。
  驱动力：技术炒作效应消退、短
期市场趋势、替代技术出现。
      时间跨度：通常较短（可能数
月到几年）。
长期稳定需求的规律（必备）
  路径：长期不变（必备属性→必
备属性）
  案例：包装的"防泄漏"属性在6
年间始终为必备属性。
  驱动力：基础功能不变（如安全
需求）、技术未发生颠覆性变革。
  时间跨度：在当前技术轨道上持
续品类全生命周期。
长期保持稳定的需求，通常属于基础功能需求或行业客户的长期追求，与人类基本需求、行业本质属性或社会
共识密切相关。是我们必须挖掘并长期坚持的目标。
长期稳定需求的规律（期望）
  路径：长期不变（期望属性→
期望属性）
  案例：亚马逊“更多选择、更
低成本、更快捷”一直是期望属性
  驱动力：行业的长期追求、行
业的本质属性
  时间跨度：在当前商业模式下
持续品类全生命周期。
定期更新KANO分类：把握好节奏
根据品类生命周期和行业特征，动态调整KANO分类：
• 品类生命周期
• 导入期：客户认知快速变化，全新特性的定义快速变化。每3-6个月进行一次KANO分析。
• 成长期：客户关注点变为特性改进，每6-12个月进行一次KANO分析。
• 成熟期：客户对特性的认知基本稳定，竞争焦点转变为成本和细分，每年或每1.5年进行一次KANO分析。
• 衰退期：产品面临淘汰或重新定义，须进行一次深入全面的KANO分析。
• 行业特征：
• 快速变化的行业：如消费电子、互联网、软件等技术更新换代快的行业，需求变化频繁，每3-6个月进
行一次KANO分析。
• 相对稳定的行业：如传统制造业、公用事业等，产品和服务相因对稳定，需求变化较慢，每12-18个月
进行一次KANO分析。
当出现以下信号时，需要更新KANO分类
信号类型具体表现与案例数据来源
用户行为数据变化某功能使用率骤降（如智能手表心率监测使用率下降50%）产品埋点、日志分析
用户反馈异常负面评价激增（如某功能的差评占比超过20%）客服工单、应用商店评论
竞品动态竞品推出新功能（如手机厂商引入卫星通信）竞品分析报告、专利数据库
技术突破
新技术的突破与成熟（AI技术成熟使语音助手从“魅力属性”变为
“期望属性”）;
某项技术的成本下降至行业平均水平的30%时（如AR眼镜显示模组价
格跌破$50），相关功能可能从魅力属性转化为期望属性
技术趋势报告、Gartner曲
线
市场趋势变化健康意识增强导致智能穿戴设备的血氧监测从“可选”变为“必备”行业白皮书、社交媒体舆情
政策法规调整
如欧盟2024年推行电子产品统一充电接口标准，原属于魅力属性的
Type-C兼容性可能突变为必备属性，需在政策生效前3个月完成模型
调整。
政府公告、法律合规部门通
知
当出现以上信号，可以结合出现异常的具体功能或技术应用场景，对客户进行针对性调研，基于调研结果，判断KANO分
类结果是否进行更新， 从而为后续产品优化迭代奠定方向。
描述赋值来源
重要度评价客户、任务都有重要度属性，重要的客户完成其
KPI的关键任务相关的需求就是重要的需求
决策链+用户任务旅程输出
满足度评价满足度是现状和预期的差距，是一个比较的结果决策链+用户任务旅程输出
KANO分类基于KANO模型的需求分类重要度-满足度矩阵
优先级基于KANO分类的优先级排序
第二个步骤完成后，需求对应的KANO分类完成了分析，将作为第三步骤的输入
到此，完成了对需求真伪的识别，对需求本质的探索以及需求的分类，需求洞察目标达
成3/4。
第二个步骤完成后
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
客户需求优先级vs产品需求优先级
客户需求优先级：基于KANO模型对客户利益、任务、痛点的排序。
产品需求优先级：基于013法则，综合考虑需求、竞争、能力，对产品需求进
行的选择。
关系：需求是产品需求优先级选择的维度之一。客户需求比产品需求的需求层
级高，是产品需求（功能、性能）的重要度和满足度的判断依据之一。
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
客户需求优先级排序方法
步骤一：基于卡诺的魅力、期望、必备属性，将识别出的客户需求归类。
步骤二：类间排序：必备需求高于期望需求高于魅力需求
步骤三：类内排序：必备需求不排（最高优先级），期望和魅力需求按需求重要度评分排序
步骤四：输出排序结果。
示例：KANO需求的优先级排序
需求三度评分卡诺分类优先级
关键节点监控5*5*5=125必备高
环路防护5*5*5+125必备高
分级分权网管5*5*5=125必备高
闲置网络资源快速回收5*4*5=100期望中
聚合告警，问题推送5*5*5=125期望高
故障节点和位置定位5*5*5=125期望高
端口合规管理5*4*4=80期望低
网络设备全生命周期管理4*3*3=36魅力低
客户需求优先级排序的口诀
先分生死：用必备型需求守住客户底线；
再算性价比：用三度评分法对期望型需求排序；
谨慎创新：魅力型需求必须通过付费意愿测试；
动态调整：跟踪头部客户需求动态，调整优先级。
需求洞察三步
步骤一 
定义问题
步骤二 
客户需求分类
步骤三 
客户需求优先级排序
⚫ 客户任务洞察：用户任务旅程图
⚫ 客户利益洞察：决策链
⚫ 识别重要度和满足度
⚫ KANO分类
⚫ 分类方法：重要度-满足度矩阵
⚫ 分类动态性：属性转化与预判
⚫ 客户需求优先级排序的内涵
⚫ 客户需求优先级排序方法
⚫ 需求洞察的输出
描述赋值来源
重要度评价客户、任务都有重要度属性，重要的客户完成其
KPI的关键任务相关的需求就是重要的需求
决策链+用户任务旅程输出
满足度评价满足度是现状和预期的差距，是一个比较的结果决策链+用户任务旅程输出
KANO分类基于KANO模型的需求分类重要度-满足度矩阵
优先级基于KANO分类的优先级排序KANO标签和重要度满意度评分
第三个步骤完成后，需求优先级排序完成。
到此，完成了对需求真伪的识别，对需求本质的探索、需求的分类以及需求优先级排序，
需求洞察目标达成4/4。
第三个步骤完成后
客户需求（可多级树形呈现）
需求ID
（需求系
统上ID）
客户需
求名称
需求所有
者（主角）
背景（场
景）
任务/目
标/动机
当前做法问题/期
望
重要度
（三度评
分）
满足度KANO分
类
125未满足期望
完善《需求规格跟踪矩阵表》的“客户需求”部分
需求洞察的输出
需求洞察与各阶段的关联关系需求洞察给各阶段的输入
提出项目支柱指明了创新方向——针对用户痛点相关的解法假设
是项目支柱的创新核心，同时支撑并提供用户侧可
能的约束条件
用户痛点（集合）（创新方向可能
只针对其中某些痛点）
用户克服障碍的时间、代价
提出价值主张需求洞察出的痛点，通过解法初设后带来的用户收
益和代价的权衡，一个/一组需求洞察的结果整合产
生了对应的价值主张
用户痛点（集合）
新老故事的收益假设（价值主张可
能只会针对部分收益假设进行提炼）
识别用户核心
需求集
通过决策链用户以及其核心用户任务的旅程图分析，
可以罗列相连的核心需求集
任务旅程地图中各步骤推导出的用
户需求
总结：需求洞察与概念工程各阶段的关系
研讨：收获和理解
对以下内容有什么收获和理解：
（1）对重要度满足度判断标准的理解
（2）对运用重要度-满足度矩阵进行KANO分析的理解。
（3）对KANO属性动态性的理解
时间：研讨15分钟，分享10分钟。
分享人：三组。每组选出代表
休息
初始概念的013选择

初始概念的013选择
1、013法则
2、013选择
0-1-3首先是一个理念
0-1-3帮助我们在思考下面这些问题时，跳出已有的思维圈圈：
你希望做出别人没有的产品，
你希望把已经成功的功能做得更好，
你希望把别人没做好的功能自己来做好
……
0-1-3 是推动产品成功的一个极简竞争策略
•必备属性与期望属性"0"短板
➢必备属性控制投入但必须达标
➢跟随阶段：无显著差距
➢领导阶段：领先半步
•期望属性至少"1"个长板
➢期望属性是影响品牌和定价的核心竞争力
➢品牌提升阶段：突出的长板
➢品牌维持阶段：持续的长板
•魅力属性"3"个以上
➢可灵活选择的小惊喜
➢单独不易溢价，优先级低于必备和期望
➢一定数量后效果指数级上升，可做长板
问题拆解为：
1、0短板：完整性策略
     必备属性如何做到控制投入但必须达标？
2、1长板：竞争策略
（1）如何选择必备属性和期望属性，做到领先半步？
（2）长期，如何持续迭代构建核心竞争力？
3、3魅力：不轻易陷入伪需求，又能抓住未来属性
看向必备和期望的动态转化趋势。
4、周期阶段：
（1）新产品首次进入市场时的013策略是什么？
 （2）产品进入新市场时的013策略是什么？
什么是0短板
• 短板：源于“木桶理论”（短板决定整体水平），强调所有需
求维度的均衡性，避免因某一环节的不足导致产品整体价值下
降。从竞争维度看，强调和竞品对比不差。
• 零短板的定义：指产品覆盖用户核心需求、市场竞争力、技术
可行性、成本可控性等所有关键维度，对比竞品不存在任何明
显短板或漏洞。
Kano模型认为：轻微投资于必备属性能大大提高满意度，而
重度投资于必备属性甚至无法让满意度达到0以上。因此，0短
板不是做到完美无缺，不是做到用户的所有功能要求都满足。
• 必备属性与期望属性"0"短板
➢ 必备属性控制投入但必须达标
➢ 跟随阶段：无显著差距
如何识别两类短板
短板的识别框架：以$APPEALS为基础，并扩展到功能、情感、社会等更多维度。
关系到客户利益的短板
导致后果是：
• 支撑客户任务达成的核心功能缺失，客户直
接不选择；
• 支撑客户任务达成的核心功能具备，但是较
竞争对手弱，导致竞争劣势，客户评估后不
选择；
客户利益短板将影响客户任务及关注指标的达成，直
接导致客户不选择或竞争劣势，所以客户利益0短板
不可妥协。
客户利益0短板是概念1阶段识别的重点。
关系到质量的短板
• DFX维度直接影响任务无法达成的：不可妥协
比如：具备功能，但可用性极差，用户放弃使用
• DFX维度影响用户体验但不影响任务达成：追求
0短板。如当下受限于时间和技术限制等不能完
全满足，需谨慎评估影响，估且有明确改进迭代
计划。
质量短板会损害产品口碑和品牌形象，影响客户复
购和产品增长，所以质量0短板需要追求。
质量0短板是概念2阶段识别的重点。
什么是1长板
• 期望属性至少"1"个长板
➢ 期望属性是影响品牌和定价的核心竞争力
➢ 品牌提升阶段：突出的长板
➢ 品牌维持阶段：持续的长板
• 长板：源于对“木桶理论”的逆向思考。与木桶理论强调
达标不同，长板理论强调取舍、单点和极致，即成功的关
键在于某个环节的“十倍速利好”、“压倒性优势” 。长
板与“核心竞争力”有关，强调在长板领域持续发力，建
立竞争壁垒（护城河）。
示例：小米如何识别长板
爆品的第一法则就是做减法，少即是多，只专注解决用户最迫切的需求，
把这一个需求做透。
小米有一条产品定义取舍法则：满足80%用户的80%需求。
——《小米创业思考》
举例：云米如何识别“独特属性”
独特属性：也就是差异化，要达到对立和冲突的程度，而且与品牌调性相符。确立标准后倒逼团队
去寻找。
1、呈现效果的对立
2、内部结构有自己的优势：
3、功能上领先：与竞品比参数。
评判标准：
1、用户足够痛：重要度-满足度矩阵（发现有的用户用纸板挡空调出风口）
2、竞争对手忽略：空调厂家没有把“舒适新风”作为重点。
初始概念的013选择
1、013法则
2、013选择
必备属性与期望属性“0”短板如何选
常见的错误：
• 没有先做市场细分就针对过大范围市场进行0短板选择。后果是，对哪一个细分市场都
有短板，无法选择、从而无法打赢。
• 直接对标竞品的进行0短板选择，未分析竞品需求对应的细分市场，导致要实现0短板
范围很大。
因此，实现0短板的方式是：
1、A客户的0短板未必是客户B的0短板。选择和判断0短板必须要针对明确目标市场的场景、
任务。根据目标市场洞察出来的必备需求和期望需求必须要做到0短板。
2、结合细分市场看竞品，不要泛泛看竞品照抄。
如何做到“控制投入但必须达标”？
成本约束范围内，选择必备属性须达到以下三条标准：
（1）必须支撑客户任务的达成。
（2）必须在用户关注的性能和指标上与竞争对手无显著差距。
（3）必须不存在质量短板，包括可用性、可服务性等。
期望属性“1”长板如何选择：市场切入点、持续发力点
工具：痛点-竞争矩阵
用于从竞争优势角度选择必备属性和期望属性。
X轴：竞争优势的强弱；
Y轴：基于重要度-满足度洞察的用户痛点
如何选择市场切入点：
1、通过需求重要度-满足度矩阵判断潜力长板范围
2、结合技术优势选定发力点
如何持续构建优势：形成良性循环
1、通过技术突破，带来需求满足度的提升，进而推动需求
重要度提升
2、通过技术突破，带来成本下降，进而推动需求重要度提
升
长期战略：
技术方案坚持长期进化，在动态竞争中逐渐成为主导设计
通过重要度-满足度矩阵判断KANO需求分类
必备1：需求重要度高，性能过剩。是必备需求，
但要避免镀金（刚好满足即可）。
必备2：典型的必备。
期望1：典型的期望。优先级更高。
期望2：期望需求，优先级低于期望1.
魅力：魅力需求。选择时要慎重。根据趋势判断
是否可以转化为期望，如果是，就是明天属性。
变化态（准必备）：当下是魅力（某个人群）或
者期望（另一个人群）。随着大众化，重要度提
升，变成必备。慎重选择。
变化态（可能消失）：或者属于过度满足、或者
属于重要度低，两者其中之一。
过时：果断放弃。
根据重要度、满足度评分，将客户需求以点状形式放在矩阵中。
本图：左上角四个框，建议慎重选择或放弃。
魅力属性“3”如何选择
客户需求重要度提升
变化
路径
必备
魅力
期望
魅力需求。选择时要慎重。根据趋势判断是否可以转化为期望，如果是，就是明天属性。
对于处于变化态的魅力需求，更要慎重选择，看其变化。
举例：小米的“明天属性”有三重内涵
1、技术锚定未来：选择代表行业演进方向的技术赛道而非简单优化现有方案。
     举例：全面屏、快充、AIoT。
2、体验超越预期：提供用户“想象之外”的便利性或情感价值。
     举例：iPhone的触控交互。
3、习惯不可逆：通过极致体验建立新标准，用户一旦使用便无法退回旧模式。
     案例：120W快充让“5分钟充电50%”成为习惯，传统18W充电被用户视为“石器时代”。
新产品首次进入市场、已有产品进入新市场时的013策略是什么？
新产品首次进入市场：此时竞争并不激烈，核心是满足创新者和早期采纳者。
• 创新者：客户重视关键新功能带来的突出价值收益，来自单点期望需求、魅力需求的超预
期满足（例如10倍好）。因此，创新者可以忍受产品不完整。
• 早期采纳者：开始关注并希望产品完整性，在期望和魅力需求满足下，帮助完成核心任务
的必备需求要得到满足。
已有产品进入新市场：
• 跨越鸿沟进入早期大众市场：产品完整性要求范围随着客户群扩大而进一步扩大，0短板要
求提高。新产品方案带来的次级问题必须得到解决才能够跨越鸿沟。
• 进入成熟市场：基于竞争全面考虑0短板，期望属性很重要，要求足够长板(越来越)。
• 衰退市场产品：通过必备和期望的“减法”，实现降本。
案例分享：013选择
内容：极光逆袭之路——动态竞争与突破。
分享者：陈秋植
时间：20分钟     
研讨：收获和理解
对以下内容有什么收获和理解：
（1）对013理念的理解
（2）对运用0短板、1长板法则进行产品需求选择的理解。
时间：研讨15分钟，分享10分钟。
分享人：三组。每组选出代表
谢谢