或解决某种需求，或采用某种方案，或应用某种技术

1、开局5分钟策略部署、运维策略管理0门槛****功能：&#x5F00;局过程：VLAN规划推荐和一键部署，访问控制策略推荐和一键部署，多出口选路策略推荐和一键部署运维阶段：访问控制策略可视化管理，多出口选路策略可视化管理价值：&#x32;、动态且主动的整网可靠性保障，上云后天生可靠****功能：&#x6574;网防环路、防私接、防IP冲突部署自动开启和动态调整价值：&#x33;、端到端的业务体验保障：动态的出口选路策略、流控策略、无线保障策略调整，新增出口线路或新增部门不用跑现场****功能：&#x591A;出口选路和流控策略的动态编排，关键终端的无线保障策略价值：&#x34;、分钟级故障定界和业务恢复****功能：&#x65E0;线体验问题和服务器访问问题故障定界，AI排障2.0（针对环路、私接小路由、IP冲突问题的定位和解决）价值：

问题简述	用户故事	发生频次	当前做法	期望
无线AP掉线问题	只在汇聚采用网管型交换机，接入采用傻瓜交换机，车间由于生产需求采用几十个环境传感器收集信息，组播报文较多，导致AP经常不定期掉线，排查近半年；	每周多次	最终将全部接入换成可网管交换机，并且做精细化的VLAN和ACL隔离才解决问题	精细化VLAN、ACL的规划和部署
办公区无线体验问题	初期网络建设的时候办公区采用全无线办公，终端采用的是原有PC+USB无线网卡的方式，在研发部门入驻后，无线网络经常出现各种卡顿、掉线的情况 ，基本无法使用，信息中心天天在处理这些问题焦头烂额	每周多次	排查几个月，最终也没有弄清楚无线为什么不行，增加布设有线网络作为办公区主要用网，无线仅作为补充	故障定界定位
广告屏掉线问题	厂区内有广告屏和服务器，由信息中心控制进行推流，推送一些学习内容或厂内的安全案例等，广告屏安装在电梯间、食堂，经常出现推流后广告屏不动的情况	每月多次	尝试重启广告屏解决，有时候要重启多次；至今不知道到问题是出在无线网不稳定，还是广告屏与服务器的连接不稳定	故障定界定位
研发或生产访问服务器异常问题定界难	主要研发部门和生产线都走有线，有时候遇到访问服务器的业务出现卡顿或者服务响应慢，员工第一反应都是网络不行了，这种事情偶发为主，但经常会遇到，因为无法在出现问题的时候去排查，我们也很难确定是服务器响应慢还是网络出问题，导致最终影响的都是对网络建设的满意度；	每年多次	尝试着跟踪服务器的资源使用情况来做一些辅助判断，基本无法排查和确定	故障定界定位

问题简述	用户故事	发生频次	当前做法	期望
生产线变化，工人自己重接网线导致环路	工厂由于人力有限，根据生产需要经常需要调整工位，工艺工程部或生产调整工位往往都需要重新拔插网线，有的时候用网的地方网线不够还会要另外接小口数交换机，这个时候由他们并不专业但又想快速满足用网需求，很可能接错线导致网络形成环路导致大面积网络瘫痪。	每月多次	首先询问故障提报人现象（大部分是生产的组长反馈MES系统扫描录入异常），工程师到现场观察交换机的端口闪烁频率（非常高则为环路），然后再现场询问近期有没有做了哪些变动，如果问到了就先把那条线断了；如果没有找到人，就只能到环路的交换机处逐条网线进行拔插（有1-2分钟的时差信号灯闪烁才会变化）来判定。每次排查影响都至少2-3个小时起（折算异常工时就过百了）。	环路预防和控制影响面
生产线网络割接耗时	制造部ATP车间生产不同时期随着客户订单产品形态和比重不一样时为了提升生产效率，工艺工程部会制定线体改造、新增线体、线体搬迁等方案，其中就包含网络的改造（主要是满足MES系统数据采集设备的用网和资料共享传输的需求），改造时有的是离散工位，有的是整体搬迁流水线。由于产线有投产日期要求，每一次改造，信息中心都要提前做预演，并预留足够的时间来处理异常，确保割接顺利。	每季度多次	经常要找放假的时间做预演，然后尽量找周末或者假期做割接，主要是为了防止出现割接后网络不通的问题，一般半小时的割接，要预留半天的时间会比较稳妥；涉及大的线体的搬迁基本都是占用各种节假日。	VLAN、ACL策略、防环路、防私接小路由等配置的快速部署
生产线频繁的临时外网放行操作	生产线上的电脑由于管理需要全部都限制了上外网，生产线上测试设备碰到问题需要厂家或客户远程调试支持，工艺工程部的TE工程师就会提交开放权限申请，网络工程师根据需要开放外网给TE进行远程调试，一般是设置了1天的有效期用完后恢复限制。	每月多次	有一个专职的IT学习这些操作，部门里只有一个人会	简单易用的放行、恢复限制操作
成品车间MES无线网络频繁掉线	成品生产车间MES无线网络干扰严重导致频繁掉线，访问MES系统卡顿，扫描信息经常无法提交，基本已经弃用（近期准备尝试用5G方案）	每周多次	无法判断哪里出了问题，只能凭经验（MES系统有线使用正常）判断是无线的问题，现场扫描发现很多信号，判断是干扰严重导致的	故障定界定位
研发临时放行访问服务器和恢复限制	为了研发的数据保密安全，研发的网络根据要求在对应的交换机接口上设置了严格的ACL进行限制，但是也会有软件需要调试软件或者需要对服务器安装开发或编译环境，在开发调试或安装环节需要快速准确的调整安全策略。一般是研发同事提出用网需求，领导情况属实审批通过后，工程师评估确认方案或到相应的网络或安全设备上进行调整，安装完环境后重新恢复配置限制。	每月多次	有一个专职的IT学习这些操作，部门里只有一个人会	简单易用的放行、恢复限制操作
员工宿舍无线上网卡顿问题	宿舍网络检测到整体带宽排查问题很困难，宿舍各设备和终端的带宽和连接数使用情况不清晰不好。暂时没什么好办法只能不断调整限制策略，逐个设备排查带宽问题	每周多次	无法判断是哪里出了问题，只能凭经验判断是人多了（之前100多人使用没问题，今年增加到400多人就频繁出问题）导致出口不够了，尝试做限制和增加宽带来解决；	故障定界定位


问题简述	用户故事	发生频次	当前做法	期望
门店没有懂网络的，分支网络断网远程沟通和排查难，经常要跑现场	运动鞋服类门店还好，店员比较年轻，其他类型的门店就麻烦了，都是3、40岁的，基本不懂网络，一出问题连接线都不知道怎么接，远程沟通很困难。当我们到现场时，往往都是简单的问题，比如打扫的时候碰掉线了，然后接线时插错口了，或者临时新增了个设备，接错网口了等等。	每月多次	由于设备离线后无法远程，分支网络基本是黑盒状态，只能先尝试远程沟通，了解现场情况，实在搞不定就只能派人去现场。1000+门店的客户，平均每月1-2趟现场，单趟成本2000元左右。	分支离线时能够结合之前的运行状态有一个较完整的分析，指导问题排查
门店终端的访问策略和刷新问题	我们（工程商）这么些年下来积累了5000+个url的策略，一般会向我们的客户做推荐部署，这些策略是让门店的终端在访问某些网站的时候走总部的出口，能够被总部的设备审计到，但门店采用的网关设备一般都比较简单，很难支持这么多策略和策略的定期更新	每个门店部署一次每年多次更新	有预算的客户采用更好的网关来满足；没预算的客户就放弃这部分方案；	在普通网关上也能够支持这样的策略


